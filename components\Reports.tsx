import React, { useState, useMemo } from 'react';
import { Category } from '../types';
import ProgressBar from './ProgressBar';
import { 
  ChartBarIcon, 
  CalendarIcon, 
  TrendingUpIcon, 
  TrendingDownIcon,
  InfoIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '../constants';

interface ReportsProps {
  categories: Category[];
  totalIncome: number;
  totalAllocated: number;
  unallocatedAmount: number;
  formatCurrency: (amount: number) => string;
  selectedCurrency: string;
  areGlobalAmountsHidden: boolean;
}

type ReportPeriod = 'current' | 'monthly' | 'quarterly' | 'yearly';
type ReportView = 'overview' | 'categories' | 'trends' | 'insights';

const Reports: React.FC<ReportsProps> = ({
  categories,
  totalIncome,
  totalAllocated,
  unallocatedAmount,
  formatCurrency,
  selectedCurrency,
  areGlobalAmountsHidden
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState<ReportPeriod>('current');
  const [selectedView, setSelectedView] = useState<ReportView>('overview');

  // Calculate report data
  const reportData = useMemo(() => {
    const totalSubcategories = categories.reduce((sum, cat) => sum + cat.subcategories.length, 0);
    const completedSubcategories = categories.reduce((sum, cat) => 
      sum + cat.subcategories.filter(sub => sub.isComplete).length, 0
    );
    const allocationPercentage = totalIncome > 0 ? (totalAllocated / totalIncome) * 100 : 0;
    const savingsRate = totalIncome > 0 ? Math.max(0, unallocatedAmount) / totalIncome * 100 : 0;
    
    // Category analysis
    const categoryAnalysis = categories.map(category => {
      const subcategoriesTotal = category.subcategories.reduce((sum, sub) => sum + sub.allocatedAmount, 0);
      const utilizationRate = category.allocatedAmount > 0 ? (subcategoriesTotal / category.allocatedAmount) * 100 : 0;
      const completionRate = category.subcategories.length > 0 ? 
        (category.subcategories.filter(sub => sub.isComplete).length / category.subcategories.length) * 100 : 0;
      
      return {
        ...category,
        subcategoriesTotal,
        utilizationRate,
        completionRate,
        remaining: category.allocatedAmount - subcategoriesTotal,
        percentageOfIncome: totalIncome > 0 ? (category.allocatedAmount / totalIncome) * 100 : 0
      };
    }).sort((a, b) => b.allocatedAmount - a.allocatedAmount);

    return {
      totalCategories: categories.length,
      totalSubcategories,
      completedSubcategories,
      allocationPercentage,
      savingsRate,
      categoryAnalysis,
      completionRate: totalSubcategories > 0 ? (completedSubcategories / totalSubcategories) * 100 : 0
    };
  }, [categories, totalIncome, totalAllocated, unallocatedAmount]);

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-slate-800 p-6 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Total Budget</p>
              <p className="text-2xl font-bold text-emerald-400">{formatCurrency(totalIncome)}</p>
              <p className="text-slate-500 text-xs mt-1">Monthly income</p>
            </div>
            <div className="w-12 h-12 bg-emerald-500/20 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-slate-800 p-6 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Allocated</p>
              <p className="text-2xl font-bold text-sky-400">{reportData.allocationPercentage.toFixed(1)}%</p>
              <p className="text-slate-500 text-xs mt-1">{formatCurrency(totalAllocated)}</p>
            </div>
            <div className="w-12 h-12 bg-sky-500/20 rounded-lg flex items-center justify-center">
              <ChartBarIcon className="w-6 h-6 text-sky-400" />
            </div>
          </div>
        </div>

        <div className="bg-slate-800 p-6 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Savings Rate</p>
              <p className="text-2xl font-bold text-amber-400">{reportData.savingsRate.toFixed(1)}%</p>
              <p className="text-slate-500 text-xs mt-1">{formatCurrency(Math.max(0, unallocatedAmount))}</p>
            </div>
            <div className="w-12 h-12 bg-amber-500/20 rounded-lg flex items-center justify-center">
              {reportData.savingsRate > 0 ? 
                <TrendingUpIcon className="w-6 h-6 text-amber-400" /> :
                <TrendingDownIcon className="w-6 h-6 text-amber-400" />
              }
            </div>
          </div>
        </div>

        <div className="bg-slate-800 p-6 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Completion</p>
              <p className="text-2xl font-bold text-purple-400">{reportData.completionRate.toFixed(1)}%</p>
              <p className="text-slate-500 text-xs mt-1">{reportData.completedSubcategories}/{reportData.totalSubcategories} tasks</p>
            </div>
            <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <CheckCircleIcon className="w-6 h-6 text-purple-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Budget Allocation Chart */}
      <div className="bg-slate-800 p-6 rounded-lg">
        <h3 className="text-xl font-semibold text-sky-400 mb-4">Budget Allocation Overview</h3>
        <div className="space-y-4">
          <div className="flex justify-between items-center text-sm">
            <span className="text-slate-400">Allocated ({reportData.allocationPercentage.toFixed(1)}%)</span>
            <span className="text-sky-400 font-medium">{formatCurrency(totalAllocated)}</span>
          </div>
          <div className="flex justify-between items-center text-sm">
            <span className="text-slate-400">Available ({(100 - reportData.allocationPercentage).toFixed(1)}%)</span>
            <span className="text-emerald-400 font-medium">{formatCurrency(Math.max(0, unallocatedAmount))}</span>
          </div>
          {unallocatedAmount < 0 && (
            <div className="flex justify-between items-center text-sm">
              <span className="text-slate-400">Over-allocated</span>
              <span className="text-red-400 font-medium">{formatCurrency(Math.abs(unallocatedAmount))}</span>
            </div>
          )}
          
          <div className="w-full bg-slate-700 rounded-full h-4 mt-4">
            <div 
              className="bg-sky-500 h-4 rounded-full transition-all duration-500 relative"
              style={{ width: `${Math.min(reportData.allocationPercentage, 100)}%` }}
            >
              {reportData.allocationPercentage > 100 && (
                <div 
                  className="bg-red-500 h-4 rounded-r-full absolute top-0"
                  style={{ 
                    left: '100%', 
                    width: `${Math.min((reportData.allocationPercentage - 100), 50)}%` 
                  }}
                ></div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Financial Health Score */}
      <div className="bg-slate-800 p-6 rounded-lg">
        <h3 className="text-xl font-semibold text-sky-400 mb-4">Financial Health Score</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className={`w-20 h-20 mx-auto rounded-full flex items-center justify-center text-2xl font-bold ${
              reportData.allocationPercentage >= 80 && reportData.allocationPercentage <= 100 ? 'bg-emerald-500/20 text-emerald-400' :
              reportData.allocationPercentage > 100 ? 'bg-red-500/20 text-red-400' :
              'bg-amber-500/20 text-amber-400'
            }`}>
              {reportData.allocationPercentage >= 80 && reportData.allocationPercentage <= 100 ? 'A' :
               reportData.allocationPercentage > 100 ? 'D' :
               reportData.allocationPercentage >= 50 ? 'B' : 'C'}
            </div>
            <p className="text-slate-300 font-medium mt-2">Budget Planning</p>
            <p className="text-slate-500 text-sm">
              {reportData.allocationPercentage >= 80 && reportData.allocationPercentage <= 100 ? 'Excellent allocation' :
               reportData.allocationPercentage > 100 ? 'Over-allocated' :
               reportData.allocationPercentage >= 50 ? 'Good progress' : 'Needs improvement'}
            </p>
          </div>

          <div className="text-center">
            <div className={`w-20 h-20 mx-auto rounded-full flex items-center justify-center text-2xl font-bold ${
              reportData.savingsRate >= 20 ? 'bg-emerald-500/20 text-emerald-400' :
              reportData.savingsRate >= 10 ? 'bg-amber-500/20 text-amber-400' :
              'bg-red-500/20 text-red-400'
            }`}>
              {reportData.savingsRate >= 20 ? 'A' :
               reportData.savingsRate >= 10 ? 'B' : 'C'}
            </div>
            <p className="text-slate-300 font-medium mt-2">Savings Rate</p>
            <p className="text-slate-500 text-sm">
              {reportData.savingsRate >= 20 ? 'Excellent savings' :
               reportData.savingsRate >= 10 ? 'Good savings' : 'Low savings'}
            </p>
          </div>

          <div className="text-center">
            <div className={`w-20 h-20 mx-auto rounded-full flex items-center justify-center text-2xl font-bold ${
              reportData.completionRate >= 80 ? 'bg-emerald-500/20 text-emerald-400' :
              reportData.completionRate >= 50 ? 'bg-amber-500/20 text-amber-400' :
              'bg-red-500/20 text-red-400'
            }`}>
              {reportData.completionRate >= 80 ? 'A' :
               reportData.completionRate >= 50 ? 'B' : 'C'}
            </div>
            <p className="text-slate-300 font-medium mt-2">Goal Progress</p>
            <p className="text-slate-500 text-sm">
              {reportData.completionRate >= 80 ? 'Great progress' :
               reportData.completionRate >= 50 ? 'On track' : 'Behind goals'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-slate-800 p-6 rounded-lg">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-semibold text-sky-400">Financial Reports</h2>
            <p className="text-slate-400 mt-1">Comprehensive insights into your budget performance</p>
          </div>
          
          {/* Period Selector */}
          <div className="flex space-x-2">
            {(['current', 'monthly', 'quarterly', 'yearly'] as ReportPeriod[]).map((period) => (
              <button
                key={period}
                onClick={() => setSelectedPeriod(period)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedPeriod === period
                    ? 'bg-sky-600 text-white'
                    : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
                }`}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* View Tabs */}
        <div className="flex space-x-1 mt-6 bg-slate-700 p-1 rounded-lg">
          {(['overview', 'categories', 'trends', 'insights'] as ReportView[]).map((view) => (
            <button
              key={view}
              onClick={() => setSelectedView(view)}
              className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedView === view
                  ? 'bg-slate-600 text-white'
                  : 'text-slate-300 hover:text-white'
              }`}
            >
              {view.charAt(0).toUpperCase() + view.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Report Content */}
      {selectedView === 'overview' && renderOverview()}
      {selectedView === 'categories' && renderCategoriesView()}
      {selectedView === 'trends' && renderTrendsView()}
      {selectedView === 'insights' && renderInsightsView()}
    </div>
  );
};

export default Reports;
